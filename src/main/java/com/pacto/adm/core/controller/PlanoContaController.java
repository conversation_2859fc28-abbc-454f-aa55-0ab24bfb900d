package com.pacto.adm.core.controller;

import com.pacto.adm.core.adapters.ProdutoAdapter;
import com.pacto.adm.core.adapters.planoconta.PlanoContaAdapter;
import com.pacto.adm.core.dto.filtros.FiltroPlanoContaDespesasJSON;
import com.pacto.adm.core.dto.filtros.FiltroPlanoContaProdutoJSON;
import com.pacto.adm.core.entities.PlanoConta;
import com.pacto.adm.core.entities.Produto;
import com.pacto.adm.core.services.interfaces.planoconta.PlanoContaService;
import com.pacto.adm.core.swagger.respostas.planoconta.ExemploRespostaListPlanoContaPaginacao;
import com.pacto.adm.core.swagger.respostas.planoconta.ExemploRespostaListProdutoPaginacao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/plano-conta")
public class PlanoContaController {

    private final PlanoContaService planoContaService;
    private final ProdutoAdapter produtoAdapter;
    private final PlanoContaAdapter planoContaAdapter;

    public PlanoContaController(PlanoContaService planoContaService, ProdutoAdapter produtoAdapter, PlanoContaAdapter planoContaAdapter) {
        this.planoContaService = planoContaService;
        this.produtoAdapter = produtoAdapter;
        this.planoContaAdapter = planoContaAdapter;
    }

    @GetMapping("/produtos")
    public ResponseEntity<EnvelopeRespostaDTO> findAllProdutos(
            @RequestParam(value = "filters", required = false) String filters,
            PaginadorDTO paginadorDTO
    ) {
        try {
            List<Produto> result = planoContaService.findAllProdutos(new FiltroPlanoContaProdutoJSON(filters), paginadorDTO);
            return ResponseEntityFactory.ok(
                    produtoAdapter.toDtos(result)
            );
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping("/despesas")
    public ResponseEntity<EnvelopeRespostaDTO> findAllDespesas(
            @RequestParam(value = "filters", required = false) String filters,
            PaginadorDTO paginadorDTO
    ) {
        try {
            List<PlanoConta> result = planoContaService.findAllDespesas(new FiltroPlanoContaDespesasJSON(filters), paginadorDTO);
            return ResponseEntityFactory.ok(planoContaAdapter.toDtos(result));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
